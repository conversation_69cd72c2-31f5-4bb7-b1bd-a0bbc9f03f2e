<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>1.8.7.2</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.wmbdaiassistant</groupId>
    <artifactId>wmbdaiassistant</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>waimai_cd_crm_bd_ai_assistant</name>

    <modules>
        <module>client</module>
        <module>server</module>
        <module>common</module>
        <module>domain</module>
        <module>infrastructure</module>
    </modules>

    <properties>
        <revision>1.0.0</revision>
        <bdaiassistant.client>1.0.10</bdaiassistant.client>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>poros-java-api-client</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>poros-client</artifactId>
                <version>0.9.23.1-RC4</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.17.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.17.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.17.2</version>
            </dependency>
            <dependency>
                <groupId>jakarta.json</groupId>
                <artifactId>jakarta.json-api</artifactId>
                <version>2.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.wmbdaiassistant</groupId>
                <artifactId>client</artifactId>
                <version>${bdaiassistant.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.wmbdaiassistant</groupId>
                <artifactId>server</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.wmbdaiassistant</groupId>
                <artifactId>common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.wmbdaiassistant</groupId>
                <artifactId>domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.wmbdaiassistant</groupId>
                <artifactId>infrastructure</artifactId>
                <version>${revision}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.doveim</groupId>
                <artifactId>waimai_service_crm_doveim_client</artifactId>
                <version>1.0.21</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.infra</groupId>
                <artifactId>waimai_service_infra_client</artifactId>
                <version>1.1.58</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.credit</groupId>
                <artifactId>credit-access-api</artifactId>
                <version>1.2.7</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.ai</groupId>
                <artifactId>friday-java-sdk</artifactId>
                <version>0.0.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.ai.mvp</groupId>
                <artifactId>mvp-facade-idl</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.canyin.risk</groupId>
                <artifactId>risk_virbius_thrift_client</artifactId>
                <version>0.12.5.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.vex.plus</groupId>
                <artifactId>retriever-client</artifactId>
                <version>0.0.3-UPDATE-INTERNAL</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.customer</groupId>
                <artifactId>waimai_service_customer_client</artifactId>
                <version>1.8.11</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai</groupId>
                <artifactId>waimai_service_econtract_client</artifactId>
                <version>1.6.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.amazonaws</groupId>
                        <artifactId>aws-java-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.scribe</groupId>
                        <artifactId>scribe-log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.poi</groupId>
                <artifactId>waimai_service_poiquery_client</artifactId>
                <version>1.0.22</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.poi</groupId>
                <artifactId>waimai_service_poi_client</artifactId>
                <version>1.9.48</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.sec</groupId>
                <artifactId>distribute-thrift-client</artifactId>
                <version>2.1.11</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-simple</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.11.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.waimai.cd.crm</groupId>
                <artifactId>waimai-crm-algorithm-engine-sdk</artifactId>
                <version>0.0.21</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.qualification</groupId>
                <artifactId>waimai_service_qualification_client</artifactId>
                <version>1.3.14</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.poisearch</groupId>
                <artifactId>waimai_service_poisearch_client</artifactId>
                <version>1.0.13</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.1</version>
            </dependency>
            <dependency>
                <groupId>ma.glasnost.orika</groupId>
                <artifactId>orika-core</artifactId>
                <version>1.5.4</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>9.7.1</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-commons</artifactId>
                <version>9.7.1</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-tree</artifactId>
                <version>9.7.1</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>0.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.waimai.crm</groupId>
                <artifactId>team-client</artifactId>
                <version>1.0.32</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>csc-access-facade-api</artifactId>
                <version>0.0.54</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.powermock</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.audit</groupId>
                <artifactId>waimai_service_audit_client</artifactId>
                <version>1.1.19</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.poibaseinfo</groupId>
                <artifactId>waimai_service_poibaseinfo_client</artifactId>
                <version>1.3.6.2</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
                <artifactId>open-sdk</artifactId>
                <version>1.0.39-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.xm</groupId>
                <artifactId>xm.alertlink</artifactId>
                <version>0.0.6-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.epbi.aigc</groupId>
                <artifactId>engine-sdk</artifactId>
                <version>1.4.0</version>
            </dependency>
            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-core</artifactId>
                <version>3.5.19</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>7.10.5-dp2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>7.10.5-dp2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--org 2.0-->
            <dependency>
                <groupId>com.sankuai.meituan.org</groupId>
                <artifactId>open-sdk</artifactId>
                <version>5.0.32</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cglib</groupId>
                        <artifactId>cglib</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>xmd-log4j2</artifactId>
                        <groupId>com.meituan.inf</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.waimai.crm</groupId>
                <artifactId>authenticate-client</artifactId>
                <version>0.3.5-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.banma.weather.admin</groupId>
                <artifactId>banma_service_weather_admin_client</artifactId>
                <version>1.1.1</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.12.0</version>
            </dependency>

            <!-- 基础rhino包 -->
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-client</artifactId>
                <version>${rhino.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <package.environment>dev</package.environment>
                <bdaiassistant.client.version>${bdaiassistant.client}-SNAPSHOT</bdaiassistant.client.version>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <package.environment>test</package.environment>
                <bdaiassistant.client.version>${bdaiassistant.client}-SNAPSHOT</bdaiassistant.client.version>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <properties>
                <package.environment>staging</package.environment>
                <bdaiassistant.client.version>${bdaiassistant.client}-SNAPSHOT</bdaiassistant.client.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <package.environment>prod</package.environment>
                <bdaiassistant.client.version>${bdaiassistant.client}</bdaiassistant.client.version>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <profiles>expand</profiles>
                                <dependencies>keep</dependencies>
                                <build>keep</build>
                            </pomElements>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
